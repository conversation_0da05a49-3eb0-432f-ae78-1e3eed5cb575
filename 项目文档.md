# AI资讯抓取与Gemini反向代理项目

## 项目概述

本项目包含两个主要功能：
1. **AI资讯抓取系统** - 自动抓取AI相关资讯并生成摘要
2. **Gemini API反向代理** - 通过Cloudflare Worker反向代理访问Gemini API

## 项目结构

### 核心文件
- `ai_tools_crawler.py` - AI资讯抓取主程序
- `daily_crawler.py` - 每日定时抓取脚本
- `config.py` - 配置文件
- `genai_proxy.py` - Gemini反向代理客户端
- `cloudflare-worker.js` - Cloudflare Worker反向代理脚本
- `setup_env.py` - 环境配置脚本
- `test_suite.py` - 统一测试套件

### 配置文件
- `.env` - 环境变量配置
- `requirements.txt` - Python依赖包

## 功能详解

### 1. AI资讯抓取系统

#### 功能特点
- 自动搜索AI相关关键词
- 抓取最新资讯内容
- 使用Gemini AI生成中文摘要
- 支持批量处理和去重
- 自动保存到文件

#### 使用方法
```bash
# 运行单次抓取
python ai_tools_crawler.py

# 运行每日定时抓取
python daily_crawler.py
```

#### 配置说明
在`config.py`中可以配置：
- 搜索关键词
- 抓取数量
- 输出格式
- API配置

### 2. Gemini API反向代理

#### 功能特点
- 通过Cloudflare Worker绕过网络限制
- 支持Cloudflare Access认证
- 兼容原生Gemini API接口
- 自动错误处理和重试

#### 部署步骤

##### 步骤1: 部署Cloudflare Worker
1. 登录到 [Cloudflare Dashboard](https://dash.cloudflare.com/)
2. 进入 "Workers & Pages"
3. 创建新的Worker
4. 复制 `cloudflare-worker.js` 中的代码到Worker编辑器
5. 保存并部署
6. 记录Worker URL

##### 步骤2: 配置Cloudflare Access（可选）
如果需要保护Worker端点：

1. **创建Access应用程序**
   - 进入 Zero Trust → Access → Applications
   - 添加新应用程序，类型选择"Self-hosted"
   - 域名设置为您的Worker域名
   - 路径设置为 `/`

2. **配置访问策略**
   - 添加策略规则类型为"Service Auth"
   - 动作设置为"Allow"

3. **创建Service Token**
   - 进入 Zero Trust → Access → Service Auth → Service Tokens
   - 创建新的Service Token
   - 复制Client ID和Client Secret

##### 步骤3: 配置环境变量
在`.env`文件中设置：
```env
# Gemini API密钥
GEMINI_API_KEY=your-gemini-api-key

# Cloudflare Access配置（如果启用）
CLOUDFLARE_ACCESS_CLIENT_ID=your-client-id
CLOUDFLARE_ACCESS_CLIENT_SECRET=your-client-secret

# Google搜索API配置（用于资讯抓取）
GOOGLE_SEARCH_API_KEY=your-google-search-api-key
GOOGLE_SEARCH_ENGINE_ID=your-search-engine-id
```

#### 使用方法
```python
from genai_proxy import GeminiProxyClient

# 创建客户端（自动从.env加载配置）
client = GeminiProxyClient("https://your-worker.your-domain.workers.dev")

# 生成内容
response = client.generate_content(
    model="gemini-2.5-pro",
    contents="你好，请介绍一下自己"
)

print(response.text)
```

## 环境配置

### 安装依赖
```bash
pip install -r requirements.txt
```

### 配置环境变量
1. 复制`.env.example`为`.env`（如果存在）
2. 或运行配置脚本：
```bash
python setup_env.py
```

### Windows定时任务配置
1. 打开"任务计划程序"
2. 创建基本任务
3. 设置触发器为每日执行
4. 操作设置为运行Python脚本：
   ```
   程序: python
   参数: daily_crawler.py
   起始位置: 项目目录路径
   ```

## 测试和诊断

运行统一测试套件：
```bash
python test_suite.py
```

测试套件包含：
1. 环境配置检查
2. API连接测试
3. Cloudflare Access认证测试
4. 完整功能测试

## 故障排除

### 常见问题

1. **API调用失败**
   - 检查API密钥是否正确
   - 确认网络连接正常
   - 验证Worker URL是否正确

2. **Cloudflare Access认证失败**
   - 检查Service Token格式是否正确
   - 确认Access策略配置正确
   - 验证Token是否已过期

3. **资讯抓取失败**
   - 检查Google搜索API配置
   - 确认搜索引擎ID正确
   - 验证网络连接

### 调试方法
1. 运行测试套件诊断问题
2. 检查日志文件 `ai_tools_crawler.log`
3. 使用详细模式运行脚本

## 注意事项

1. **API限制**
   - 注意各API的调用限制
   - 合理设置请求间隔

2. **安全性**
   - 不要在代码中硬编码API密钥
   - 定期更新Service Token

3. **性能优化**
   - 适当设置批处理大小
   - 使用缓存避免重复请求

## 更新日志

- 初始版本：基础资讯抓取功能
- v1.1：添加Gemini反向代理支持
- v1.2：集成Cloudflare Access认证
- v1.3：统一测试套件和文档整理

## 许可证

本项目仅供学习和研究使用。请遵守相关API的使用条款。

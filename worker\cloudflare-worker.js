// Cloudflare Worker 反向代理 Gemini API
export default {
  async fetch(request, env, ctx) {
    // 允许的域名（可选，用于CORS）
    const allowedOrigins = ['*']; // 或者指定具体域名
    
    // 处理 CORS 预检请求
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        status: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization, x-goog-api-key',
          'Access-Control-Max-Age': '86400',
        },
      });
    }

    try {
      // 获取原始请求的URL
      const url = new URL(request.url);
      
      // 构建目标URL - 将请求转发到Google AI API
      const targetUrl = `https://generativelanguage.googleapis.com${url.pathname}${url.search}`;
      
      // 复制原始请求的headers
      const headers = new Headers(request.headers);
      
      // 移除可能导致问题的headers
      headers.delete('host');
      headers.delete('cf-ray');
      headers.delete('cf-connecting-ip');
      headers.delete('cf-visitor');
      headers.delete('cf-ipcountry');
      
      // 创建新的请求
      const modifiedRequest = new Request(targetUrl, {
        method: request.method,
        headers: headers,
        body: request.body,
      });

      // 发送请求到Google AI API
      const response = await fetch(modifiedRequest);
      
      // 复制响应
      const modifiedResponse = new Response(response.body, {
        status: response.status,
        statusText: response.statusText,
        headers: response.headers,
      });

      // 添加CORS headers
      modifiedResponse.headers.set('Access-Control-Allow-Origin', '*');
      modifiedResponse.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      modifiedResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-goog-api-key');

      return modifiedResponse;

    } catch (error) {
      return new Response(JSON.stringify({
        error: 'Proxy Error',
        message: error.message,
        timestamp: new Date().toISOString()
      }), {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      });
    }
  },
};

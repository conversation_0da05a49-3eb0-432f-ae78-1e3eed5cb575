# AI资讯抓取与Gemini反向代理项目

集成AI资讯抓取和Gemini API反向代理功能的综合工具包。

## 项目功能

1. **AI资讯抓取系统** - 自动抓取AI相关资讯并生成摘要
2. **Gemini API反向代理** - 通过Cloudflare Worker反向代理访问Gemini API

## 功能特点

### AI资讯抓取系统
- 🔍 **智能搜索**: 使用Google Custom Search API搜索最新AI资讯
- 🤖 **AI验证**: 通过Gemini AI分析内容，判断是否为真正的AI编程工具
- 🌐 **中文支持**: 自动将英文内容翻译为中文
- 📝 **结果保存**: 将验证通过的工具信息保存到带日期的txt文件
- ⏰ **定时执行**: 配合Windows任务计划程序实现每日自动抓取
- ⚡ **批量处理**: 每次处理10条资讯，大幅减少API调用次数

### Gemini反向代理系统
- 🌐 **网络代理**: 通过Cloudflare Worker绕过网络限制
- 🔐 **安全认证**: 支持Cloudflare Access Service Token认证
- 🔄 **API兼容**: 完全兼容原生Gemini API接口
- ⚡ **高性能**: 利用Cloudflare全球CDN网络

## 文件说明

### 核心文件
- `ai_tools_crawler.py` - AI资讯抓取主程序
- `daily_crawler.py` - 每日定时抓取脚本
- `genai_proxy.py` - Gemini反向代理客户端
- `cloudflare-worker.js` - Cloudflare Worker反向代理脚本
- `config.py` - 配置文件
- `setup_env.py` - 环境配置脚本
- `test_suite.py` - 统一测试套件

### 配置和文档
- `.env` - 环境变量配置文件
- `requirements.txt` - Python依赖包列表
- `项目文档.md` - 详细的项目文档和使用指南

## 快速开始

### 1. 环境设置

首先运行环境配置脚本：

```bash
python setup_env.py
```

这将自动：
- 安装必要的依赖包
- 创建 `.env` 配置文件模板

### 2. 获取API密钥

#### Google Custom Search API密钥
1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建新项目或选择现有项目
3. 启用 "Custom Search API"
4. 创建API密钥

#### 自定义搜索引擎ID
1. 访问 [Programmable Search Engine](https://programmablesearchengine.google.com/)
2. 点击"添加"创建新的搜索引擎
3. 设置搜索范围为"搜索整个网络"
4. 记录搜索引擎ID

#### Gemini API密钥
1. 访问 [Google AI Studio](https://aistudio.google.com/apikey)
2. 创建API密钥

### 3. 配置环境变量

编辑 `.env` 文件，填入您的API密钥：

```env
# Gemini API密钥（必需）
GEMINI_API_KEY=your_gemini_api_key_here

# Google Custom Search API密钥（资讯抓取功能需要）
GOOGLE_SEARCH_API_KEY=your_actual_api_key_here
GOOGLE_SEARCH_ENGINE_ID=your_search_engine_id_here

# Cloudflare Access配置（如果启用反向代理保护）
CLOUDFLARE_ACCESS_CLIENT_ID=your_client_id
CLOUDFLARE_ACCESS_CLIENT_SECRET=your_client_secret
```

### 4. 运行和测试

#### 运行测试套件
```bash
python test_suite.py
```

#### AI资讯抓取
```bash
# 立即执行一次抓取
python ai_tools_crawler.py

# 每日定时抓取
python daily_crawler.py
```

#### Gemini反向代理
```python
from genai_proxy import GeminiProxyClient

# 创建客户端
client = GeminiProxyClient("https://your-worker.your-domain.workers.dev")

# 生成内容
response = client.generate_content(
    model="gemini-2.5-pro",
    contents="你好，请介绍一下自己"
)
print(response.text)
```

详细的部署和配置说明请参考 `项目文档.md`。

## 输出结果

程序会生成以下文件：

- `ai_tools_YYYYMMDD.txt` - 当日发现的AI编程工具信息
- `ai_tools_crawler.log` - 主程序运行日志
- `daily_crawler.log` - 定时任务运行日志

### 结果文件示例

```
AI编程工具信息汇总 - 2024年01月15日
==================================================

1. GitHub Copilot X - 新一代AI编程助手
链接: https://github.com/features/copilot
分析结果:
1. 是否为AI编程工具: 是
2. 工具类型: AI代码生成和补全工具
3. 主要功能: 基于GPT-4的智能代码生成，支持多种编程语言的实时代码补全
4. 是否值得关注: 是，GitHub官方产品，功能强大
5. 简要总结: GitHub推出的新一代AI编程助手，能够理解上下文并生成高质量代码
----------------------------------------

总计找到 5 个AI编程工具
生成时间: 2024-01-15 14:30:25
```

## 搜索关键词

程序使用以下关键词进行搜索：

- AI coding tools 2024
- new AI programming assistant
- AI code generator beta
- AI developer tools launch
- AI IDE extension new
- AI code completion tool
- programming AI assistant
- code AI helper new
- AI coding platform beta
- developer AI tools 2024

## API限制说明

- **Google Custom Search API**: 每天100次免费查询
- **Gemini API**: 根据您的配额限制
- 建议每天运行一次，避免超出免费限制

## 故障排除

### 常见问题

1. **API密钥错误**
   - 检查 `.env` 文件中的密钥是否正确
   - 确认API已启用且密钥有效

2. **搜索结果为空**
   - 检查自定义搜索引擎配置
   - 确认搜索范围设置为"搜索整个网络"

3. **Gemini分析失败**
   - 检查Gemini API密钥和配额
   - 查看日志文件了解具体错误信息

### 日志查看

```bash
# 查看主程序日志
tail -f ai_tools_crawler.log

# 查看定时任务日志
tail -f daily_crawler.log
```

## 自定义配置

您可以修改 `ai_tools_crawler.py` 中的以下参数：

- `search_keywords`: 搜索关键词列表
- `days_back`: 搜索最近几天的内容（默认3天）
- 搜索结果数量和其他参数

## 许可证

本项目仅供学习和个人使用。请遵守相关API的使用条款。

## 更新日志

- v1.0.0 - 初始版本，支持基本的搜索和分析功能
- v1.1.0 - 添加Gemini反向代理支持
- v1.2.0 - 集成Cloudflare Access认证
- v1.3.0 - 统一测试套件和文档整理

## 详细文档

更多详细信息请参考：
- `项目文档.md` - 完整的项目文档和配置指南
- `test_suite.py` - 运行测试和诊断工具

# Windows任务计划程序设置说明

## 如何设置每日自动抓取AI编程工具信息

### 1. 打开任务计划程序

1. 按 `Win + R` 打开运行对话框
2. 输入 `taskschd.msc` 并按回车
3. 或者在开始菜单搜索"任务计划程序"

### 2. 创建基本任务

1. 在右侧操作面板点击"创建基本任务..."
2. 输入任务名称：`AI编程工具每日抓取`
3. 输入描述：`每日自动抓取最新的AI编程工具信息`
4. 点击"下一步"

### 3. 设置触发器

1. 选择"每天"
2. 点击"下一步"
3. 设置开始时间（建议上午9:00）
4. 设置开始日期为今天或明天
5. 每隔：1天
6. 点击"下一步"

### 4. 设置操作

1. 选择"启动程序"
2. 点击"下一步"
3. 程序或脚本：浏览并选择Python可执行文件
   - 通常位置：`C:\Python\python.exe` 或 `C:\Users\<USER>\AppData\Local\Programs\Python\Python3x\python.exe`
   - 或者在命令提示符中输入 `where python` 查看Python路径
4. 添加参数：`daily_crawler.py`
5. 起始于：设置为您的项目文件夹路径（例如：`E:\python脚本\抓取资讯`）
6. 点击"下一步"

### 5. 完成设置

1. 勾选"当单击完成时，打开此任务属性的对话框"
2. 点击"完成"

### 6. 高级设置（可选）

在打开的属性对话框中：

#### 常规选项卡：
- 勾选"不管用户是否登录都要运行"
- 勾选"使用最高权限运行"

#### 条件选项卡：
- 取消勾选"只有在计算机使用交流电源时才启动此任务"
- 勾选"如果计算机开始使用电池电源，停止任务"

#### 设置选项卡：
- 勾选"允许按需运行任务"
- 勾选"如果请求后任务还在运行，强行将其停止"
- 停止任务，如果运行时间超过：1小时

### 7. 测试任务

1. 在任务计划程序库中找到刚创建的任务
2. 右键点击任务，选择"运行"
3. 检查是否正常执行

### 8. 查看执行结果

任务执行后，检查以下文件：
- `daily_crawler.log` - 执行日志
- `ai_tools_YYYYMMDD.txt` - 当日抓取结果

### 9. 故障排除

#### 任务未执行：
1. 检查Python路径是否正确
2. 检查起始目录是否正确
3. 查看任务历史记录中的错误信息

#### 任务执行失败：
1. 检查 `daily_crawler.log` 文件中的错误信息
2. 确认API密钥配置正确
3. 手动运行 `python daily_crawler.py` 测试

### 10. 手动执行命令

如果需要手动执行，可以使用以下命令：

```cmd
# 切换到项目目录
cd /d "E:\python脚本\抓取资讯"

# 执行抓取任务
python daily_crawler.py
```

### 11. 批处理文件（可选）

您也可以创建一个批处理文件 `run_crawler.bat`：

```batch
@echo off
cd /d "E:\python脚本\抓取资讯"
python daily_crawler.py
pause
```

然后在任务计划程序中直接运行这个批处理文件。

## 注意事项

1. **API限制**：Google Custom Search API每天有100次免费查询限制
2. **网络连接**：确保计算机在执行时间有网络连接
3. **Python环境**：确保Python环境正确配置
4. **权限问题**：如果遇到权限问题，尝试以管理员身份运行
5. **日志监控**：定期检查日志文件，确保任务正常执行

## 推荐执行时间

- **上午9:00** - 获取夜间发布的新工具信息
- **下午2:00** - 获取上午发布的信息
- **晚上8:00** - 获取下午发布的信息

建议选择一个固定时间，避免频繁执行超出API限制。

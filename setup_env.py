#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境配置脚本
用于设置API密钥和安装依赖
"""

import os
import subprocess
import sys

def install_dependencies():
    """安装必要的依赖包"""
    dependencies = [
        'google-generativeai',
        'requests',
        'python-dotenv'
    ]
    
    print("正在安装依赖包...")
    for package in dependencies:
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✓ {package} 安装成功")
        except subprocess.CalledProcessError:
            print(f"✗ {package} 安装失败")

def create_env_file():
    """创建环境变量配置文件"""
    env_content = """# API密钥配置文件
# 请填入您的实际API密钥

# Google Custom Search API密钥
GOOGLE_SEARCH_API_KEY=your_google_search_api_key_here

# Google自定义搜索引擎ID
GOOGLE_SEARCH_ENGINE_ID=your_search_engine_id_here

# Gemini API密钥
GEMINI_API_KEY=your_gemini_api_key_here
"""
    
    with open('.env', 'w', encoding='utf-8') as f:
        f.write(env_content)
    
    print("✓ 已创建 .env 配置文件")
    print("请编辑 .env 文件，填入您的实际API密钥")

def setup_instructions():
    """显示设置说明"""
    instructions = """
=== AI编程工具抓取器设置说明 ===

1. 获取Google Custom Search API密钥:
   - 访问: https://console.cloud.google.com/
   - 创建新项目或选择现有项目
   - 启用 "Custom Search API"
   - 创建API密钥

2. 创建自定义搜索引擎:
   - 访问: https://programmablesearchengine.google.com/
   - 点击"添加"创建新的搜索引擎
   - 设置搜索范围为"搜索整个网络"
   - 记录搜索引擎ID

3. 获取Gemini API密钥:
   - 访问: https://aistudio.google.com/apikey
   - 创建API密钥

4. 配置环境变量:
   - 编辑 .env 文件
   - 填入上述获取的API密钥

5. 运行程序:
   python ai_tools_crawler.py

注意事项:
- Google Custom Search API每天有100次免费查询限制
- 建议每天运行一次，避免超出限制
- 程序会自动保存结果到带日期的txt文件中
"""
    
    print(instructions)

def main():
    """主函数"""
    print("=== AI编程工具抓取器环境设置 ===\n")
    
    # 安装依赖
    install_dependencies()
    print()
    
    # 创建环境配置文件
    create_env_file()
    print()
    
    # 显示设置说明
    setup_instructions()

if __name__ == "__main__":
    main()

import os
import requests
import json
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class GeminiProxyClient:
    def __init__(self, proxy_url, api_key=None, cf_client_id=None, cf_client_secret=None):
        """
        初始化Gemini代理客户端

        Args:
            proxy_url: Cloudflare Worker的URL
            api_key: Gemini API密钥，如果不提供则从环境变量GEMINI_API_KEY获取
            cf_client_id: Cloudflare Access Client ID，如果不提供则从环境变量CLOUDFLARE_ACCESS_CLIENT_ID获取
            cf_client_secret: Cloudflare Access Client Secret，如果不提供则从环境变量CLOUDFLARE_ACCESS_CLIENT_SECRET获取
        """
        self.proxy_url = proxy_url.rstrip('/')
        self.api_key = api_key or os.getenv('GEMINI_API_KEY')
        self.cf_client_id = cf_client_id or os.getenv('CLOUDFLARE_ACCESS_CLIENT_ID')
        self.cf_client_secret = cf_client_secret or os.getenv('CLOUDFLARE_ACCESS_CLIENT_SECRET')

        if not self.api_key:
            raise ValueError("API key is required. Set GEMINI_API_KEY environment variable or pass api_key parameter.")

        if not self.cf_client_id:
            raise ValueError("Cloudflare Access Client ID is required. Set CLOUDFLARE_ACCESS_CLIENT_ID environment variable or pass cf_client_id parameter.")

        if not self.cf_client_secret:
            raise ValueError("Cloudflare Access Client Secret is required. Set CLOUDFLARE_ACCESS_CLIENT_SECRET environment variable or pass cf_client_secret parameter.")
    
    def generate_content(self, model="gemini-2.5-pro", contents="", **kwargs):
        """
        生成内容
        
        Args:
            model: 模型名称
            contents: 输入内容
            **kwargs: 其他参数
        """
        # 构建API端点URL
        endpoint = f"/v1beta/models/{model}:generateContent"
        url = f"{self.proxy_url}{endpoint}"
        
        # 构建请求体
        payload = {
            "contents": [
                {
                    "parts": [
                        {
                            "text": contents
                        }
                    ]
                }
            ]
        }
        
        # 添加其他参数
        if kwargs:
            payload.update(kwargs)
        
        # 设置请求头
        headers = {
            'Content-Type': 'application/json',
            'x-goog-api-key': self.api_key,
            'CF-Access-Client-Id': self.cf_client_id,
            'CF-Access-Client-Secret': self.cf_client_secret
        }
        
        try:
            # 发送请求
            response = requests.post(url, json=payload, headers=headers, timeout=30)
            response.raise_for_status()
            
            # 解析响应
            result = response.json()
            
            # 提取文本内容
            if 'candidates' in result and len(result['candidates']) > 0:
                candidate = result['candidates'][0]
                if 'content' in candidate and 'parts' in candidate['content']:
                    parts = candidate['content']['parts']
                    if len(parts) > 0 and 'text' in parts[0]:
                        return GeminiResponse(parts[0]['text'], result)
            
            # 如果没有找到预期的结构，返回原始响应
            return GeminiResponse("", result)
            
        except requests.exceptions.RequestException as e:
            raise Exception(f"Request failed: {str(e)}")
        except json.JSONDecodeError as e:
            raise Exception(f"Failed to parse response: {str(e)}")

class GeminiResponse:
    """响应对象，模拟原始genai库的响应格式"""
    def __init__(self, text, raw_response):
        self.text = text
        self.raw_response = raw_response

# 使用示例
if __name__ == "__main__":
    # 替换为您的Cloudflare Worker URL
    PROXY_URL = "https://asgemini.atreide.workers.dev"

    try:
        # 创建客户端（会自动从.env文件加载API密钥和Cloudflare Access token）
        client = GeminiProxyClient(PROXY_URL)

        # 生成内容
        response = client.generate_content(
            model="gemini-2.5-pro",
            contents="你是什么模型"
        )

        print(response.text)

    except Exception as e:
        print(f"错误: {e}")
        print("请确保已在.env文件中设置GEMINI_API_KEY、CLOUDFLARE_ACCESS_CLIENT_ID和CLOUDFLARE_ACCESS_CLIENT_SECRET")

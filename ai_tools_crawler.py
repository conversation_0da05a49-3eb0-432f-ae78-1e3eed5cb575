#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI编程工具信息抓取器
每日获取最新的AI编程工具信息，通过Gemini验证并保存结果
"""

import os
import json
import requests
import time
from datetime import datetime, timedelta
from google import genai
import logging
from dotenv import load_dotenv
import config

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ai_tools_crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class AIToolsCrawler:
    def __init__(self):
        """初始化爬虫"""
        # API配置 - 请在环境变量中设置这些值
        self.google_api_key = os.getenv('GOOGLE_SEARCH_API_KEY')
        self.search_engine_id = os.getenv('GOOGLE_SEARCH_ENGINE_ID')
        self.gemini_api_key = os.getenv('GEMINI_API_KEY')
        
        if not all([self.google_api_key, self.search_engine_id, self.gemini_api_key]):
            raise ValueError("请设置环境变量: GOOGLE_SEARCH_API_KEY, GOOGLE_SEARCH_ENGINE_ID, GEMINI_API_KEY")
        
        # 初始化Gemini客户端
        self.gemini_client = genai.Client()

        # 从配置文件加载参数
        self.search_keywords = config.SEARCH_KEYWORDS
        self.batch_size = config.BATCH_SIZE
        self.search_days_back = config.SEARCH_DAYS_BACK
        self.search_results_per_keyword = config.SEARCH_RESULTS_PER_KEYWORD
        self.search_delay = config.SEARCH_DELAY
        self.analysis_delay = config.ANALYSIS_DELAY
        self.gemini_model = config.GEMINI_MODEL

        # Google Custom Search API基础URL
        self.search_url = "https://www.googleapis.com/customsearch/v1"
        
    def search_google(self, query, days_back=None):
        """
        使用Google Custom Search API搜索
        
        Args:
            query: 搜索关键词
            days_back: 搜索最近几天的内容
            
        Returns:
            搜索结果列表
        """
        try:
            if days_back is None:
                days_back = self.search_days_back

            params = {
                'key': self.google_api_key,
                'cx': self.search_engine_id,
                'q': query,
                'num': self.search_results_per_keyword,
                'dateRestrict': f'd{days_back}',  # 限制最近N天
                'sort': 'date',  # 按日期排序
                'lr': 'lang_en',  # 主要搜索英文内容
            }
            
            response = requests.get(self.search_url, params=params)
            response.raise_for_status()
            
            data = response.json()
            results = []
            
            if 'items' in data:
                for item in data['items']:
                    result = {
                        'title': item.get('title', ''),
                        'link': item.get('link', ''),
                        'snippet': item.get('snippet', ''),
                        'displayLink': item.get('displayLink', ''),
                        'formattedUrl': item.get('formattedUrl', '')
                    }
                    results.append(result)
                    
            logging.info(f"搜索关键词 '{query}' 找到 {len(results)} 个结果")
            return results
            
        except requests.exceptions.RequestException as e:
            logging.error(f"搜索请求失败: {e}")
            return []
        except Exception as e:
            logging.error(f"搜索过程出错: {e}")
            return []
    
    def save_raw_results(self, all_results):
        """
        保存原始搜索结果到文件

        Args:
            all_results: 所有搜索结果列表
        """
        today = datetime.now().strftime("%Y%m%d")
        filename = f"raw_search_results_{today}.txt"

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"原始搜索结果 - {datetime.now().strftime('%Y年%m月%d日')}\n")
                f.write("=" * 50 + "\n\n")

                for i, result in enumerate(all_results, 1):
                    f.write(f"{i}. {result['title']}\n")
                    f.write(f"链接: {result['link']}\n")
                    f.write(f"摘要: {result['snippet']}\n")
                    f.write(f"来源: {result['displayLink']}\n")
                    f.write("-" * 40 + "\n\n")

                f.write(f"\n总计找到 {len(all_results)} 条搜索结果\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")

            logging.info(f"原始搜索结果已保存到: {filename}")

        except Exception as e:
            logging.error(f"保存原始结果失败: {e}")

    def analyze_batch_with_gemini(self, results_batch):
        """
        使用Gemini批量分析多条资讯

        Args:
            results_batch: 资讯列表，每个元素包含title, snippet, link

        Returns:
            list: 分析结果列表
        """
        try:
            # 构建资讯列表
            news_list = ""
            for i, result in enumerate(results_batch, 1):
                news_list += f"""
资讯 {i}:
标题: {result['title']}
摘要: {result['snippet']}
链接: {result['link']}
---
"""

            # 使用配置文件中的提示词模板
            prompt = config.BATCH_ANALYSIS_PROMPT_TEMPLATE.format(news_list=news_list)

            response = self.gemini_client.models.generate_content(
                model=self.gemini_model,
                contents=prompt
            )

            analysis_text = response.text

            # 解析批量分析结果
            return self.parse_batch_analysis(analysis_text, results_batch)

        except Exception as e:
            logging.error(f"Gemini批量分析失败: {e}")
            # 返回失败结果
            return [{
                'is_ai_tool': False,
                'analysis': f"分析失败: {str(e)}",
                'title': result['title'],
                'url': result['link'],
                'snippet': result['snippet']
            } for result in results_batch]

    def parse_batch_analysis(self, analysis_text, results_batch):
        """
        解析批量分析结果

        Args:
            analysis_text: Gemini返回的分析文本
            results_batch: 原始资讯列表

        Returns:
            list: 解析后的结果列表
        """
        try:
            parsed_results = []

            # 按照"资讯X分析:"分割文本
            sections = analysis_text.split('资讯')[1:]  # 去掉第一个空元素

            for i, section in enumerate(sections):
                if i >= len(results_batch):
                    break

                result = results_batch[i]

                # 检查是否为AI编程工具
                is_ai_tool = False
                if '是否为AI编程工具:' in section:
                    ai_tool_line = [line for line in section.split('\n') if '是否为AI编程工具:' in line]
                    if ai_tool_line and '是' in ai_tool_line[0]:
                        is_ai_tool = True

                parsed_result = {
                    'is_ai_tool': is_ai_tool,
                    'analysis': f"资讯{i+1}分析:\n{section.strip()}",
                    'title': result['title'],
                    'url': result['link'],
                    'snippet': result['snippet']
                }

                parsed_results.append(parsed_result)

            # 如果解析的结果数量不够，补充剩余的
            while len(parsed_results) < len(results_batch):
                idx = len(parsed_results)
                parsed_results.append({
                    'is_ai_tool': False,
                    'analysis': "解析失败",
                    'title': results_batch[idx]['title'],
                    'url': results_batch[idx]['link'],
                    'snippet': results_batch[idx]['snippet']
                })

            return parsed_results

        except Exception as e:
            logging.error(f"解析批量分析结果失败: {e}")
            # 返回默认结果
            return [{
                'is_ai_tool': False,
                'analysis': f"解析失败: {str(e)}",
                'title': result['title'],
                'url': result['link'],
                'snippet': result['snippet']
            } for result in results_batch]
    
    def save_results(self, verified_tools):
        """
        保存验证通过的工具信息到文件
        
        Args:
            verified_tools: 验证通过的工具列表
        """
        if not verified_tools:
            logging.info("没有找到符合条件的AI编程工具")
            return
        
        # 生成文件名（包含日期）
        today = datetime.now().strftime("%Y%m%d")
        filename = f"ai_tools_{today}.txt"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"AI编程工具信息汇总 - {datetime.now().strftime('%Y年%m月%d日')}\n")
                f.write("=" * 50 + "\n\n")
                
                for i, tool in enumerate(verified_tools, 1):
                    f.write(f"{i}. {tool['title']}\n")
                    f.write(f"链接: {tool['url']}\n")
                    f.write(f"分析结果:\n{tool['analysis']}\n")
                    f.write("-" * 40 + "\n\n")
                
                f.write(f"\n总计找到 {len(verified_tools)} 个AI编程工具\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            
            logging.info(f"结果已保存到文件: {filename}")
            
        except Exception as e:
            logging.error(f"保存文件失败: {e}")
    
    def run(self):
        """运行主程序"""
        logging.info("开始抓取AI编程工具信息...")

        all_results = []
        verified_tools = []

        # 第一步：搜索所有关键词
        logging.info("第一步：搜索资讯...")
        for keyword in self.search_keywords:
            logging.info(f"正在搜索: {keyword}")

            # 搜索
            search_results = self.search_google(keyword)
            all_results.extend(search_results)

            # 避免API调用过于频繁
            time.sleep(self.search_delay)

        # 去重（基于URL）
        unique_results = {}
        for result in all_results:
            url = result['link']
            if url not in unique_results:
                unique_results[url] = result

        unique_results_list = list(unique_results.values())
        logging.info(f"去重后共有 {len(unique_results_list)} 个唯一结果")

        # 第二步：保存原始搜索结果
        logging.info("第二步：保存原始搜索结果...")
        self.save_raw_results(unique_results_list)

        # 第三步：批量分析
        logging.info(f"第三步：批量分析（每批 {self.batch_size} 条）...")

        # 分批处理
        for i in range(0, len(unique_results_list), self.batch_size):
            batch = unique_results_list[i:i + self.batch_size]
            batch_num = i // self.batch_size + 1
            total_batches = (len(unique_results_list) + self.batch_size - 1) // self.batch_size

            logging.info(f"正在分析第 {batch_num}/{total_batches} 批（{len(batch)} 条资讯）...")

            # 批量分析
            batch_results = self.analyze_batch_with_gemini(batch)

            # 筛选AI编程工具
            for result in batch_results:
                if result['is_ai_tool']:
                    verified_tools.append(result)
                    logging.info(f"✓ 发现AI编程工具: {result['title'][:50]}...")

            # 避免API调用过于频繁
            if i + self.batch_size < len(unique_results_list):
                logging.info(f"等待{self.analysis_delay}秒后处理下一批...")
                time.sleep(self.analysis_delay)

        # 第四步：保存最终结果
        logging.info("第四步：保存分析结果...")
        self.save_results(verified_tools)

        logging.info(f"抓取完成！共发现 {len(verified_tools)} 个AI编程工具")
        logging.info(f"Gemini API调用次数: {(len(unique_results_list) + self.batch_size - 1) // self.batch_size} 次")
        return verified_tools

def main():
    """主函数"""
    try:
        crawler = AIToolsCrawler()
        results = crawler.run()
        
        print(f"\n抓取完成！")
        print(f"共发现 {len(results)} 个AI编程工具")
        print(f"详细信息已保存到文件中")
        
    except Exception as e:
        logging.error(f"程序运行失败: {e}")
        print(f"程序运行失败: {e}")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI编程工具信息抓取器
每日获取最新的AI编程工具信息，通过Gemini验证并保存结果
"""

import os
import json
import requests
import time
from datetime import datetime, timedelta
from google import genai
import logging
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ai_tools_crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class AIToolsCrawler:
    def __init__(self):
        """初始化爬虫"""
        # API配置 - 请在环境变量中设置这些值
        self.google_api_key = os.getenv('GOOGLE_SEARCH_API_KEY')
        self.search_engine_id = os.getenv('GOOGLE_SEARCH_ENGINE_ID')
        self.gemini_api_key = os.getenv('GEMINI_API_KEY')
        
        if not all([self.google_api_key, self.search_engine_id, self.gemini_api_key]):
            raise ValueError("请设置环境变量: GOOGLE_SEARCH_API_KEY, GOOGLE_SEARCH_ENGINE_ID, GEMINI_API_KEY")
        
        # 初始化Gemini客户端
        self.gemini_client = genai.Client()
        
        # 搜索关键词列表
        self.search_keywords = [
            "AI coding tools 2024",
            "new AI programming assistant",
            "AI code generator beta",
            "AI developer tools launch",
            "AI IDE extension new",
            "AI code completion tool",
            "programming AI assistant",
            "code AI helper new",
            "AI coding platform beta",
            "developer AI tools 2024"
        ]
        
        # Google Custom Search API基础URL
        self.search_url = "https://www.googleapis.com/customsearch/v1"
        
    def search_google(self, query, days_back=3):
        """
        使用Google Custom Search API搜索
        
        Args:
            query: 搜索关键词
            days_back: 搜索最近几天的内容
            
        Returns:
            搜索结果列表
        """
        try:
            # 计算日期范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days_back)
            
            params = {
                'key': self.google_api_key,
                'cx': self.search_engine_id,
                'q': query,
                'num': 10,  # 每次搜索返回10个结果
                'dateRestrict': f'd{days_back}',  # 限制最近N天
                'sort': 'date',  # 按日期排序
                'lr': 'lang_en',  # 主要搜索英文内容
            }
            
            response = requests.get(self.search_url, params=params)
            response.raise_for_status()
            
            data = response.json()
            results = []
            
            if 'items' in data:
                for item in data['items']:
                    result = {
                        'title': item.get('title', ''),
                        'link': item.get('link', ''),
                        'snippet': item.get('snippet', ''),
                        'displayLink': item.get('displayLink', ''),
                        'formattedUrl': item.get('formattedUrl', '')
                    }
                    results.append(result)
                    
            logging.info(f"搜索关键词 '{query}' 找到 {len(results)} 个结果")
            return results
            
        except requests.exceptions.RequestException as e:
            logging.error(f"搜索请求失败: {e}")
            return []
        except Exception as e:
            logging.error(f"搜索过程出错: {e}")
            return []
    
    def analyze_with_gemini(self, title, snippet, url):
        """
        使用Gemini分析内容是否为AI编程工具
        
        Args:
            title: 网页标题
            snippet: 网页摘要
            url: 网页链接
            
        Returns:
            dict: 包含分析结果的字典
        """
        try:
            prompt = f"""
请分析以下网页内容，判断是否是关于AI编程工具的信息。请用中文回复。

网页标题: {title}
网页摘要: {snippet}
网页链接: {url}

请按照以下格式回复：
1. 是否为AI编程工具: [是/否]
2. 工具类型: [如果是AI编程工具，请说明是什么类型的工具，如代码生成、代码补全、IDE插件等]
3. 主要功能: [简要描述主要功能，如果内容是英文请翻译为中文]
4. 是否值得关注: [是/否，说明理由]
5. 简要总结: [用1-2句话总结这个工具的核心价值]

请确保回复内容为中文，如果原文是英文请翻译为中文。
"""
            
            response = self.gemini_client.models.generate_content(
                model="gemini-2.5-flash",
                contents=prompt
            )
            
            analysis_text = response.text
            
            # 简单解析Gemini的回复
            is_ai_tool = "是" in analysis_text.split('\n')[0] if analysis_text else False
            
            return {
                'is_ai_tool': is_ai_tool,
                'analysis': analysis_text,
                'title': title,
                'url': url,
                'snippet': snippet
            }
            
        except Exception as e:
            logging.error(f"Gemini分析失败: {e}")
            return {
                'is_ai_tool': False,
                'analysis': f"分析失败: {str(e)}",
                'title': title,
                'url': url,
                'snippet': snippet
            }
    
    def save_results(self, verified_tools):
        """
        保存验证通过的工具信息到文件
        
        Args:
            verified_tools: 验证通过的工具列表
        """
        if not verified_tools:
            logging.info("没有找到符合条件的AI编程工具")
            return
        
        # 生成文件名（包含日期）
        today = datetime.now().strftime("%Y%m%d")
        filename = f"ai_tools_{today}.txt"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"AI编程工具信息汇总 - {datetime.now().strftime('%Y年%m月%d日')}\n")
                f.write("=" * 50 + "\n\n")
                
                for i, tool in enumerate(verified_tools, 1):
                    f.write(f"{i}. {tool['title']}\n")
                    f.write(f"链接: {tool['url']}\n")
                    f.write(f"分析结果:\n{tool['analysis']}\n")
                    f.write("-" * 40 + "\n\n")
                
                f.write(f"\n总计找到 {len(verified_tools)} 个AI编程工具\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            
            logging.info(f"结果已保存到文件: {filename}")
            
        except Exception as e:
            logging.error(f"保存文件失败: {e}")
    
    def run(self):
        """运行主程序"""
        logging.info("开始抓取AI编程工具信息...")
        
        all_results = []
        verified_tools = []
        
        # 遍历所有搜索关键词
        for keyword in self.search_keywords:
            logging.info(f"正在搜索: {keyword}")
            
            # 搜索
            search_results = self.search_google(keyword)
            all_results.extend(search_results)
            
            # 避免API调用过于频繁
            time.sleep(1)
        
        # 去重（基于URL）
        unique_results = {}
        for result in all_results:
            url = result['link']
            if url not in unique_results:
                unique_results[url] = result
        
        logging.info(f"去重后共有 {len(unique_results)} 个唯一结果")
        
        # 使用Gemini分析每个结果
        for i, (url, result) in enumerate(unique_results.items(), 1):
            logging.info(f"正在分析 {i}/{len(unique_results)}: {result['title'][:50]}...")
            
            analysis = self.analyze_with_gemini(
                result['title'],
                result['snippet'],
                result['link']
            )
            
            if analysis['is_ai_tool']:
                verified_tools.append(analysis)
                logging.info(f"✓ 发现AI编程工具: {result['title'][:50]}...")
            
            # 避免API调用过于频繁
            time.sleep(2)
        
        # 保存结果
        self.save_results(verified_tools)
        
        logging.info(f"抓取完成！共发现 {len(verified_tools)} 个AI编程工具")
        return verified_tools

def main():
    """主函数"""
    try:
        crawler = AIToolsCrawler()
        results = crawler.run()
        
        print(f"\n抓取完成！")
        print(f"共发现 {len(results)} 个AI编程工具")
        print(f"详细信息已保存到文件中")
        
    except Exception as e:
        logging.error(f"程序运行失败: {e}")
        print(f"程序运行失败: {e}")

if __name__ == "__main__":
    main()

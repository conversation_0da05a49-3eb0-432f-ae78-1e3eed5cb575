#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一测试套件 - AI资讯抓取与Gemini反向代理项目
"""

import os
import sys
import requests
import json
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class TestSuite:
    def __init__(self):
        self.proxy_url = "https://asgemini.atreide.workers.dev"
        self.results = []
    
    def print_header(self, title):
        """打印测试标题"""
        print(f"\n{'='*60}")
        print(f"🧪 {title}")
        print(f"{'='*60}")
    
    def print_result(self, test_name, success, message=""):
        """打印测试结果"""
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name}: {status}")
        if message:
            print(f"   {message}")
        self.results.append((test_name, success, message))
    
    def test_environment(self):
        """测试环境配置"""
        self.print_header("环境配置检查")
        
        # 检查必要的环境变量
        required_vars = {
            'GEMINI_API_KEY': '必需 - Gemini API密钥',
            'CLOUDFLARE_ACCESS_CLIENT_ID': '可选 - Cloudflare Access Client ID',
            'CLOUDFLARE_ACCESS_CLIENT_SECRET': '可选 - Cloudflare Access Client Secret',
            'GOOGLE_SEARCH_API_KEY': '可选 - Google搜索API密钥',
            'GOOGLE_SEARCH_ENGINE_ID': '可选 - Google搜索引擎ID'
        }
        
        for var, desc in required_vars.items():
            value = os.getenv(var)
            if var == 'GEMINI_API_KEY':
                # 必需变量
                self.print_result(f"环境变量 {var}", bool(value), desc)
            else:
                # 可选变量
                status = "已设置" if value else "未设置"
                self.print_result(f"环境变量 {var}", True, f"{desc} - {status}")
        
        # 检查依赖包
        try:
            import requests
            import google.generativeai
            self.print_result("Python依赖包", True, "所有必需包已安装")
        except ImportError as e:
            self.print_result("Python依赖包", False, f"缺少依赖: {e}")
    
    def test_cloudflare_access(self):
        """测试Cloudflare Access配置"""
        self.print_header("Cloudflare Access认证测试")
        
        client_id = os.getenv('CLOUDFLARE_ACCESS_CLIENT_ID')
        client_secret = os.getenv('CLOUDFLARE_ACCESS_CLIENT_SECRET')
        
        if not client_id or not client_secret:
            self.print_result("Access配置", True, "未配置Cloudflare Access（跳过测试）")
            return
        
        # 测试Access认证
        headers = {
            'CF-Access-Client-Id': client_id,
            'CF-Access-Client-Secret': client_secret,
            'User-Agent': 'Test-Suite/1.0'
        }
        
        try:
            response = requests.get(f"{self.proxy_url}/", headers=headers, timeout=10)
            
            if 'text/html' in response.headers.get('Content-Type', ''):
                if 'Sign in' in response.text or 'Cloudflare Access' in response.text:
                    self.print_result("Access认证", False, "返回登录页面，Service Token未被识别")
                else:
                    self.print_result("Access认证", True, "成功通过Access认证")
            else:
                self.print_result("Access认证", True, "成功通过Access认证")
                
        except Exception as e:
            self.print_result("Access认证", False, f"请求失败: {e}")
    
    def test_gemini_proxy(self):
        """测试Gemini反向代理"""
        self.print_header("Gemini反向代理测试")
        
        api_key = os.getenv('GEMINI_API_KEY')
        if not api_key:
            self.print_result("Gemini代理", False, "缺少GEMINI_API_KEY")
            return
        
        try:
            from genai_proxy import GeminiProxyClient
            
            client = GeminiProxyClient(self.proxy_url)
            
            # 测试简单的API调用
            response = client.generate_content(
                model="gemini-2.5-pro",
                contents="请简单回答：你是什么模型？"
            )
            
            if response.text and len(response.text) > 0:
                self.print_result("Gemini代理", True, f"成功获取响应: {response.text[:50]}...")
            else:
                self.print_result("Gemini代理", False, "响应为空")
                
        except Exception as e:
            self.print_result("Gemini代理", False, f"调用失败: {e}")
    
    def test_direct_gemini(self):
        """测试直接Gemini API调用"""
        self.print_header("直接Gemini API测试")
        
        api_key = os.getenv('GEMINI_API_KEY')
        if not api_key:
            self.print_result("直接Gemini", False, "缺少GEMINI_API_KEY")
            return
        
        try:
            from google import genai
            
            client = genai.Client(api_key=api_key)
            response = client.models.generate_content(
                model="gemini-2.5-pro",
                contents="请简单回答：你是什么模型？"
            )
            
            if response.text and len(response.text) > 0:
                self.print_result("直接Gemini", True, f"成功获取响应: {response.text[:50]}...")
            else:
                self.print_result("直接Gemini", False, "响应为空")
                
        except Exception as e:
            self.print_result("直接Gemini", False, f"调用失败: {e}")
    
    def test_crawler_config(self):
        """测试爬虫配置"""
        self.print_header("资讯爬虫配置测试")
        
        try:
            import config
            
            # 检查配置文件
            self.print_result("配置文件", True, "config.py加载成功")
            
            # 检查搜索配置
            if hasattr(config, 'SEARCH_KEYWORDS'):
                self.print_result("搜索关键词", True, f"已配置 {len(config.SEARCH_KEYWORDS)} 个关键词")
            else:
                self.print_result("搜索关键词", False, "未找到SEARCH_KEYWORDS配置")
            
            # 检查Google搜索API
            google_api_key = os.getenv('GOOGLE_SEARCH_API_KEY')
            google_engine_id = os.getenv('GOOGLE_SEARCH_ENGINE_ID')
            
            if google_api_key and google_engine_id:
                self.print_result("Google搜索API", True, "已配置Google搜索API")
            else:
                self.print_result("Google搜索API", True, "未配置Google搜索API（可选功能）")
                
        except ImportError:
            self.print_result("配置文件", False, "无法加载config.py")
        except Exception as e:
            self.print_result("配置文件", False, f"配置检查失败: {e}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始运行测试套件...")
        
        self.test_environment()
        self.test_cloudflare_access()
        self.test_gemini_proxy()
        self.test_direct_gemini()
        self.test_crawler_config()
        
        # 汇总结果
        self.print_header("测试结果汇总")
        
        passed = sum(1 for _, success, _ in self.results if success)
        total = len(self.results)
        
        print(f"总测试数: {total}")
        print(f"通过: {passed}")
        print(f"失败: {total - passed}")
        print(f"成功率: {passed/total*100:.1f}%")
        
        if passed == total:
            print("\n🎉 所有测试通过！系统配置正确。")
        else:
            print(f"\n⚠️  有 {total - passed} 个测试失败，请检查配置。")
            print("\n失败的测试:")
            for name, success, message in self.results:
                if not success:
                    print(f"  - {name}: {message}")

def show_menu():
    """显示测试菜单"""
    print("\n" + "="*60)
    print("🧪 AI资讯抓取与Gemini反向代理 - 测试套件")
    print("="*60)
    print("1. 运行所有测试")
    print("2. 环境配置检查")
    print("3. Cloudflare Access认证测试")
    print("4. Gemini反向代理测试")
    print("5. 直接Gemini API测试")
    print("6. 资讯爬虫配置测试")
    print("0. 退出")
    print("-"*60)

def main():
    """主函数"""
    suite = TestSuite()
    
    while True:
        show_menu()
        choice = input("请选择测试项目 (0-6): ").strip()
        
        if choice == '0':
            print("👋 退出测试套件")
            break
        elif choice == '1':
            suite.run_all_tests()
        elif choice == '2':
            suite.test_environment()
        elif choice == '3':
            suite.test_cloudflare_access()
        elif choice == '4':
            suite.test_gemini_proxy()
        elif choice == '5':
            suite.test_direct_gemini()
        elif choice == '6':
            suite.test_crawler_config()
        else:
            print("❌ 无效选择，请重新输入")
        
        input("\n按回车键继续...")

if __name__ == "__main__":
    main()

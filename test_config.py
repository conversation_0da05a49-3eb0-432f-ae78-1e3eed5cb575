#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置测试脚本
用于验证API密钥和配置是否正确
"""

import os
import requests
from google import genai
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_gemini_api():
    """测试Gemini API"""
    try:
        api_key = os.getenv('GEMINI_API_KEY')
        if not api_key:
            print("❌ GEMINI_API_KEY 未设置")
            return False
        
        client = genai.Client()
        response = client.models.generate_content(
            model="gemini-2.5-flash",
            contents="请用中文回复：你好，这是一个测试"
        )
        
        print("✅ Gemini API 测试成功")
        print(f"回复: {response.text[:100]}...")
        return True
        
    except Exception as e:
        print(f"❌ Gemini API 测试失败: {e}")
        return False

def test_google_search_api():
    """测试Google Custom Search API"""
    try:
        api_key = os.getenv('GOOGLE_SEARCH_API_KEY')
        engine_id = os.getenv('GOOGLE_SEARCH_ENGINE_ID')
        
        if not api_key:
            print("❌ GOOGLE_SEARCH_API_KEY 未设置")
            return False
        
        if not engine_id:
            print("❌ GOOGLE_SEARCH_ENGINE_ID 未设置")
            return False
        
        url = "https://www.googleapis.com/customsearch/v1"
        params = {
            'key': api_key,
            'cx': engine_id,
            'q': 'AI coding tools test',
            'num': 1
        }
        
        response = requests.get(url, params=params)
        response.raise_for_status()
        
        data = response.json()
        if 'items' in data and len(data['items']) > 0:
            print("✅ Google Custom Search API 测试成功")
            print(f"找到结果: {data['items'][0]['title'][:50]}...")
            return True
        else:
            print("⚠️ Google Custom Search API 连接成功，但没有搜索结果")
            return True
            
    except Exception as e:
        print(f"❌ Google Custom Search API 测试失败: {e}")
        return False

def test_environment():
    """测试环境配置"""
    print("=== API配置测试 ===\n")
    
    # 检查.env文件
    if os.path.exists('.env'):
        print("✅ .env 文件存在")
    else:
        print("❌ .env 文件不存在，请先运行 python setup_env.py")
        return False
    
    # 测试各个API
    gemini_ok = test_gemini_api()
    print()
    
    google_ok = test_google_search_api()
    print()
    
    if gemini_ok and google_ok:
        print("🎉 所有API配置正确，可以开始使用！")
        print("运行命令: python ai_tools_crawler.py")
        return True
    else:
        print("❌ 部分API配置有问题，请检查.env文件中的密钥")
        return False

def main():
    """主函数"""
    test_environment()

if __name__ == "__main__":
    main()

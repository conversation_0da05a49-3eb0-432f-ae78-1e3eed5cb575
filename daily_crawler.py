#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
每日定时抓取脚本
可以设置为Windows任务计划程序或Linux cron任务
"""

import os
import sys
import time
import schedule
from datetime import datetime
import logging
from ai_tools_crawler import AIToolsCrawler

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('daily_crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def run_daily_crawl():
    """执行每日抓取任务"""
    try:
        logging.info("开始执行每日AI编程工具抓取任务...")
        
        crawler = AIToolsCrawler()
        results = crawler.run()
        
        logging.info(f"每日抓取任务完成！发现 {len(results)} 个AI编程工具")
        
        # 可以在这里添加邮件通知或其他后续处理
        
    except Exception as e:
        logging.error(f"每日抓取任务失败: {e}")

def run_scheduler():
    """运行定时调度器"""
    # 设置每天上午9点执行
    schedule.every().day.at("09:00").do(run_daily_crawl)
    
    logging.info("定时任务已启动，每天上午9点执行抓取任务")
    logging.info("按 Ctrl+C 停止程序")
    
    try:
        while True:
            schedule.run_pending()
            time.sleep(60)  # 每分钟检查一次
    except KeyboardInterrupt:
        logging.info("程序已停止")

def run_once():
    """立即执行一次抓取"""
    logging.info("立即执行一次抓取任务...")
    run_daily_crawl()

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == "--now":
        # 立即执行
        run_once()
    else:
        # 定时执行
        run_scheduler()

if __name__ == "__main__":
    main()

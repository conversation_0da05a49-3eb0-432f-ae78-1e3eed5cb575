#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
每日抓取脚本
专为Windows任务计划程序设计的简化版本
"""

import sys
from datetime import datetime
import logging
from ai_tools_crawler import AIToolsCrawler

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('daily_crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def run_daily_crawl():
    """执行每日抓取任务"""
    try:
        logging.info("开始执行每日AI编程工具抓取任务...")

        crawler = AIToolsCrawler()
        results = crawler.run()

        logging.info(f"每日抓取任务完成！发现 {len(results)} 个AI编程工具")

        # 返回结果数量，用于任务计划程序的状态检查
        return len(results)

    except Exception as e:
        logging.error(f"每日抓取任务失败: {e}")
        # 返回错误代码
        sys.exit(1)

def main():
    """主函数"""
    logging.info("启动每日AI编程工具抓取任务")
    result_count = run_daily_crawl()
    logging.info(f"任务执行完成，共发现 {result_count} 个AI编程工具")

if __name__ == "__main__":
    main()
